using System.Collections.Generic;

namespace NafaPlace.Cart.Domain
{
    public class ShoppingCart
    {
        public string UserId { get; set; }
        public List<CartItem> Items { get; set; } = new List<CartItem>();

        // Coupon information
        public string? CouponCode { get; set; }
        public decimal CouponDiscount { get; set; } = 0;
        public string? CouponDescription { get; set; }

        public ShoppingCart()
        {
            UserId = string.Empty;
        }

        public ShoppingCart(string userId)
        {
            UserId = userId;
        }
    }
}